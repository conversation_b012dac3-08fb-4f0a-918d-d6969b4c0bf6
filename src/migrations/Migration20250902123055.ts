import { Migration } from '@mikro-orm/migrations';

export class Migration20250902123055 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create index "transactions_team_id_index" on "transactions" ("team_id");`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 0;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index "transactions_team_id_index";`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756544583148;`);
  }

}
