import { ScoutingRequest, ScoutingRequestType } from '@/entities/ScoutingRequest.js';

/**
 * Repository for scouting requests
 */
export interface ScoutingRequestRepository {
  /**
   * Create a new scouting request
   * @param gameworldId The gameworld ID
   * @param teamId The ID of the team making the request
   * @param managerId The ID of the manager making the request
   * @param type The type of scouting request
   * @param targetId The ID of the target (player, team, or league)
   * @param processAfter The time after which the request can be processed
   * @returns The created scouting request
   */
  createScoutingRequest(
    gameworldId: string,
    teamId: string,
    managerId: string,
    type: ScoutingRequestType,
    targetId: string,
    processAfter: number
  ): Promise<ScoutingRequest>;

  /**
   * Get pending scouting requests
   * @param limit The maximum number of requests to return
   * @returns Array of pending scouting requests
   */
  getPendingScoutingRequests(limit?: number): Promise<ScoutingRequest[]>;

  /**
   * Mark a scouting request as processed
   * @param requestId The ID of the request to mark as processed
   * @returns The updated scouting request
   */
  markScoutingRequestAsProcessed(requestId: string): Promise<ScoutingRequest>;

  /**
   * Get scouting requests by team
   * @param gameworldId The gameworld ID
   * @param teamId The ID of the team
   * @param limit The maximum number of requests to return
   * @returns Array of scouting requests for the team
   */
  getScoutingRequestsByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number
  ): Promise<ScoutingRequest[]>;

  /**
   * Delete a scouting request
   * @param requestId The ID of the request to delete
   * @returns Promise that resolves when the request is deleted
   */
  deleteScoutingRequest(requestId: string): Promise<void>;
}
